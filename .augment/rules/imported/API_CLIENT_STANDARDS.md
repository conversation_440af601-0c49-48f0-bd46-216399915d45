---
type: "agent_requested"
description: "Example description"
---
# DeskCRM API 客户端开发规范

## 概述

本文档定义了 DeskCRM 项目中 API 客户端 (`client.go`) 的标准开发规范，确保代码的一致性、可维护性和可读性。

## 目录结构

每个 API 客户端应按以下结构组织：

```
deskcrm/api/{service_name}/
├── client.go      # 主要的客户端实现
├── struct.go      # 数据结构定义
└── client_test.go # 单元测试（可选）
```

## 文件结构规范

### 1. 包声明和导入

```go
package {service_name}

import (
    // 标准库导入
    "encoding/json"
    "errors"
    "fmt"
    
    // 项目内部导入
    "deskcrm/api"
    "deskcrm/conf"
    "deskcrm/libs/utils"
    "deskcrm/stru/{domain}"
    
    // 第三方库导入
    "git.zuoyebang.cc/pkg/golib/v2/base"
    "git.zuoyebang.cc/pkg/golib/v2/zlog"
    "github.com/gin-gonic/gin"
)
```

**导入顺序规范：**
1. 标准库
2. 项目内部包（按字母顺序）
3. 第三方库（按字母顺序）
4. 各组之间用空行分隔

### 2. 客户端结构体定义

```go
type Client struct {
    cli *base.ApiClient
}
```

**规范要求：**
- 结构体名称必须为 `Client`
- 必须包含 `cli *base.ApiClient` 字段
- 如需扩展，可添加其他字段，但要保持简洁

### 3. 构造函数

```go
// NewClient create Client instance
func NewClient() *Client {
    c := &Client{
        cli: conf.API.{ServiceName},
    }
    return c
}
```

**规范要求：**
- 函数名必须为 `NewClient`
- 返回类型为 `*Client`
- 注释格式：`// NewClient create Client instance`
- 使用对应的配置：`conf.API.{ServiceName}`

### 4. 常量定义

```go
const (
    getOrderListApi     = "/billing/api/v1/order/list"
    getUserInfoApi      = "/billing/api/v1/user/info"
    defaultTimeout      = 30
    maxRetryCount       = 3
)
```

**命名规范：**
- API 路径常量：小写开头，驼峰命名，以 `Api` 结尾
- 其他常量：小写开头，驼峰命名
- 所有常量都是包内私有的（小写开头）
- 常量定义位置：在 `Client` 结构体和 `NewClient` 函数之后

### 5. 方法实现

#### 5.1 方法签名规范

```go
// GetOrderList 获取订单列表
// 对应PHP的 Api_Billing::orderList 方法
func (c *Client) GetOrderList(ctx *gin.Context, studentUid int64, startTime, endTime int64) ([]course.TradeInfo, error) {
    // 实现代码
}
```

**规范要求：**
- 方法名：大写开头，驼峰命名
- 第一个参数必须是 `ctx *gin.Context`
- 返回值最后一个必须是 `error`
- 注释包含方法描述和对应的 PHP 方法（如果有）

#### 5.2 标准实现模式

```go
func (c *Client) GetOrderList(ctx *gin.Context, studentUid int64, startTime, endTime int64) ([]course.TradeInfo, error) {
    // 1. 构建请求参数
    req := map[string]interface{}{
        "studentUid": studentUid,
        "startTime":  startTime,
        "endTime":    endTime,
    }

    // 2. 设置请求选项
    opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
    utils.DecorateHttpOptions(ctx, &opts)

    // 3. 发送请求
    res, err := c.cli.HttpPost(ctx, getOrderListApi, opts)
    if err != nil {
        zlog.Warnf(ctx, "GetOrderList request err: %v", err)
        return nil, err
    }

    // 4. 检查 HTTP 状态码
    if err = api.ApiHttpCode(ctx, res); err != nil {
        return nil, err
    }

    // 5. 解析响应
    var resp []course.TradeInfo
    if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
        return nil, err
    }

    return resp, nil
}
```

## 配置管理

### 1. 添加配置项

在 `deskcrm/conf/conf.go` 的 `TApi` 结构体中添加对应的配置项：

```go
type TApi struct {
    // ... 其他配置
    Billing           *base.ApiClient `yaml:"billing"`
}
```

### 2. 配置文件

在 `api.yaml` 配置文件中添加对应的服务配置。

## 错误处理规范

### 1. 日志记录

```go
// 请求错误
zlog.Warnf(ctx, "GetOrderList request err: %v", err)

// 解析错误  
zlog.Warnf(ctx, "GetOrderList decode response err: %v", err)

// 业务错误
zlog.Errorf(ctx, "GetOrderList business error: %v", err)
```

### 2. 错误返回

- 所有方法都必须返回 `error`
- 错误信息要清晰明确
- 保持错误信息的一致性

## 数据结构规范

### 1. 请求参数

```go
// 使用 map[string]interface{} 构建请求参数
req := map[string]interface{}{
    "studentUid": studentUid,
    "startTime":  startTime,
    "endTime":    endTime,
}
```

### 2. 响应结构

- 响应结构体定义在 `struct.go` 文件中
- 使用项目统一的数据结构（如 `deskcrm/stru/` 下的结构体）

## 测试规范

### 1. 单元测试

- 测试文件命名：`client_test.go`
- 测试函数命名：`TestClient_MethodName`
- 覆盖正常流程和异常流程

### 2. Mock 数据

- 对于外部依赖，使用 Mock 数据进行测试
- Mock 数据要真实可信

## 最佳实践

### 1. 性能优化

- 合理使用连接池
- 避免不必要的序列化/反序列化
- 适当使用缓存

### 2. 安全考虑

- 敏感信息不要记录到日志
- 参数验证要充分
- 防止注入攻击

### 3. 可维护性

- 代码结构清晰
- 注释完整准确
- 遵循项目编码规范

## 示例模板

参考 `deskcrm/api/billing/client.go` 作为标准模板。

## 常见问题和解决方案

### 1. 配置问题

**问题：** `conf.API.ServiceName undefined`

**解决方案：**
1. 在 `deskcrm/conf/conf.go` 的 `TApi` 结构体中添加对应字段
2. 在 `api.yaml` 配置文件中添加服务配置
3. 确保配置项命名与代码中使用的一致

### 2. 编码问题

**问题：** 请求编码方式选择

**解决方案：**
- Form 编码：`Encode: base.EncodeForm`（适用于简单参数）
- JSON 编码：`Encode: base.EncodeJson`（适用于复杂结构）
- 根据目标服务的要求选择合适的编码方式

### 3. 超时处理

**问题：** 请求超时设置

**解决方案：**
```go
opts := base.HttpRequestOptions{
    RequestBody: req,
    Encode:      base.EncodeForm,
    Timeout:     30 * time.Second, // 设置超时时间
}
```

## 迁移指南

### 从 PHP 迁移到 Go

1. **分析 PHP 代码结构**
   - 找到对应的 PHP API 类
   - 分析方法签名和参数
   - 理解业务逻辑

2. **创建 Go 客户端**
   - 按照本规范创建 `client.go`
   - 实现对应的方法
   - 保持接口语义一致

3. **数据结构映射**
   - PHP 数组 → Go map 或 struct
   - PHP 对象 → Go struct
   - 注意数据类型转换

4. **测试验证**
   - 对比 PHP 和 Go 的返回结果
   - 确保业务逻辑正确
   - 性能测试

## 参考资料

- [Go 编码规范](https://golang.org/doc/effective_go.html)
- [项目架构文档](./CLAUDE.md)
- [API 基础库文档](../api/base.go)
