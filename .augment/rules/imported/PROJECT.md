---
type: "agent_requested"
description: "Example description"
---
# CLAUDE.md - DeskCRM Go Project Analysis

## Project Overview
**DeskCRM** is a sophisticated CRM (Customer Relationship Management) module written in Go, designed as part of a workstation/desk system. The project implements a microservice architecture with extensive external service integrations, focusing on student data management, course analytics, and configurable data visualization through the ARK system.

## Architecture Patterns

### 1. Clean Architecture & Domain-Driven Design
- **Service Layer**: Domain-specific services organized by business context
- **Repository Pattern**: Database entities and data access patterns
- **Controller Pattern**: HTTP handlers separated by API type (internal/UI)
- **Middleware Pattern**: Cross-cutting concerns (auth, recovery, debugging)

### 2. Microservice Architecture
- **API Gateway Pattern**: Centralized external service integration
- **Service Registry**: 20+ external service endpoints
- **Circuit Breaker**: Fault tolerance with fusing mechanisms
- **Load Balancing**: Multiple database and service configurations

### 3. Hexagonal Architecture
- **Ports & Adapters**: Clear separation between business logic and infrastructure
- **Dependency Injection**: Resource initialization in helpers package
- **Interface-based Design**: Service contracts with multiple implementations

## Directory Structure

### Core Application Structure
```
deskcrm/
├── main.go                    # Application entry point with Cobra CLI
├── cmd/                       # Command-line interface
├── conf/                      # Configuration management
├── controllers/               # HTTP handlers and routing
├── service/                   # Business logic layer
├── models/                    # Database entities and structures
├── api/                       # External API client implementations
├── middleware/                # Cross-cutting concerns
├── helpers/                   # Infrastructure initialization
├── router/                    # HTTP and MQ routing
├── util/                      # Utility functions
├── stru/                      # Data structures and DTOs
├── libs/                      # Internal libraries
├── pkg/                       # Shared packages
└── sql/                       # Database migrations and schemas
```

### Service Architecture
- **`arkBase/`**: Core ARK analytics engine with complex data processing
  - `arkConfig/`: Configuration and rule management
  - `dataQuery/`: Data source querying and caching
  - `filterFactory/`: Dynamic filtering system
  - `formatFactory/`: Data formatting and presentation
  - `gray/`: Feature flag and gradual rollout
  - `stable/`: Circuit breaker and fault tolerance
- **`innerapi/`**: Internal API services for system integration
- **`ui/`**: User interface backend services

## Configuration Management

### Configuration Files
- **`config.yaml`**: Basic server configuration (port, logging, pprof)
- **`api.yaml`**: External API client configurations (20+ services)
- **`resource.yaml`**: Database, Redis, Elasticsearch, COS configurations
- **`custom.yaml`**: Business-specific configurations and rate limiting

### Key Configuration Features
- Multi-environment support (dev, test, production)
- Rate limiting with token bucket algorithm
- Resource pooling for databases and connections
- Cloud storage integration (Tencent COS, Baidu BOS)

## Database & External Integrations

### Database Connections
- **Primary MySQL**: `bzr_fudao` (homework_fudao database)
- **Secondary MySQL**: `duxuesc` (homework_zhibo_duxuesc database)
- **Activity MySQL**: `lpcactive` (homework_fwyy_lpcactive database)

### External Services Integration
- **DataProxy**: Data aggregation service
- **ZbCore**: Core business services (DAL, DAU, DAT, DAS)
- **Mercury**: Data management platform
- **Mesh**: Service mesh integration
- **Tower**: Infrastructure services
- **ExamCore**: Examination system
- **Moat**: Security and access control
- **Various APIs**: 20+ external service integrations

### Storage & Caching
- **Redis**: Session management and caching
- **Elasticsearch**: Search and analytics
- **COS**: Cloud Object Storage for file management

## Key Features & Business Logic

### 1. ARK Analytics System
- **Dynamic Data Filtering**: Complex rule-based filtering system
- **Data Formatting**: Configurable data presentation layers
- **Field Mapping**: Dynamic field configuration and mapping
- **Template System**: Configurable data display templates
- **Gray Release**: Feature flag system for gradual rollouts

### 2. Student Management
- **Course Enrollment**: Student-course relationship management
- **Lesson Tracking**: Individual lesson progress and attendance
- **Interview Management**: Student interview scheduling and tracking
- **Performance Analytics**: Student performance data aggregation

### 3. Data Export System
- **Async Export**: Background task processing for large datasets
- **Task Management**: Export job status tracking and management
- **File Generation**: Excel/CSV export capabilities
- **Download Management**: Secure file download with expiration

### 4. Multi-tenant Support
- **Business Selection**: Middleware for business context switching
- **Permission Management**: Role-based access control
- **Data Isolation**: Tenant-specific data segregation

## Technology Stack

### Core Framework & Libraries
- **Gin**: HTTP web framework
- **GORM**: ORM for database operations
- **Cobra**: CLI framework
- **Zlog**: Structured logging
- **json-iterator**: High-performance JSON processing
- **go-playground/validator**: Request validation
- **sync.singleflight**: Cache optimization
- **golang.org/x/sync**: Concurrency utilities

### External Dependencies
- **Elasticsearch**: Search and analytics
- **Redis**: Caching and session management
- **RocketMQ**: Message queue for async processing
- **Tencent Cloud COS**: Object storage
- **Prometheus**: Metrics and monitoring

## Testing Strategy

### Test Coverage
- **Unit Tests**: Service layer testing with mocks
- **Integration Tests**: API client testing
- **Performance Tests**: Load testing for critical paths
- **End-to-End Tests**: Complete workflow testing

### Test Files
- Service layer tests: `service/*/test.go`
- API client tests: `api/*/client_test.go`
- Utility tests: `libs/*/test.go`

## Build & Deployment

### Docker Configuration
- **Multi-stage Build**: Optimized for production
- **Base Images**: Alpine Linux for minimal footprint
- **Build Arguments**: Commit SHA and app name injection
- **Security**: Non-root user execution

### Development Workflow
- **Makefile**: Automated tasks (test, build, lint, format)
- **Pre-commit Hooks**: Code quality enforcement
- **Version Management**: Git-based versioning
- **CI/CD**: GitLab CI integration ready

### Monitoring & Observability
- **Health Checks**: Readiness and liveness probes
- **Metrics**: Prometheus integration
- **Profiling**: pprof for performance analysis
- **Distributed Tracing**: Request tracing capabilities

## API Structure

### Internal APIs (`/innerapi/`)
- **ARK Services**: Data analytics and filtering
- **Export Services**: Data export and download
- **Template Services**: Configuration management
- **Course Services**: Education domain services

### UI APIs (`/ui/`)
- **Student Services**: Student management endpoints
- **Course Services**: Course and lesson management
- **Filter Services**: Dynamic filtering configuration
- **Collection Services**: Data aggregation endpoints

## Security Features

### Authentication & Authorization
- **JWT Tokens**: Token-based authentication
- **Role-based Access**: Permission management
- **Business Context**: Multi-tenant security
- **API Key Management**: External service authentication

### Data Protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Output sanitization
- **Rate Limiting**: DoS protection

## Performance Optimization

### Caching Strategy
- **Multi-level Caching**: Redis + in-memory caching
- **Cache Invalidation**: Smart cache management
- **Query Optimization**: Efficient database queries
- **Connection Pooling**: Resource optimization

### Concurrency Management
- **Goroutine Pools**: Controlled concurrency
- **Singleflight**: Duplicate request elimination
- **Async Processing**: Background job processing
- **Circuit Breaker**: Fault tolerance

## Development Guidelines

### Code Organization
- **Package Structure**: Clear separation of concerns
- **Interface Design**: Contract-based development
- **Error Handling**: Consistent error patterns
- **Logging**: Structured logging with context

### Best Practices
- **Configuration Management**: Environment-specific configs
- **Resource Management**: Proper resource cleanup
- **Testing**: Comprehensive test coverage
- **Documentation**: Clear code documentation

## Future Considerations

### Scalability
- **Horizontal Scaling**: Container orchestration ready
- **Database Sharding**: Multi-database support
- **Service Mesh**: Microservice communication
- **Event-driven Architecture**: Async processing

### Maintenance
- **Monitoring**: Comprehensive observability
- **Alerting**: Proactive issue detection
- **Backup & Recovery**: Data protection strategies
- **Version Management**: Backward compatibility

## Getting Started

### Prerequisites
- Go 1.22+
- Docker & Docker Compose
- MySQL 5.7+
- Redis 5.0+
- Elasticsearch 7.0+

### Development Setup
1. Clone the repository
2. Copy and configure environment files
3. Run `make init` to set up pre-commit hooks
4. Run `make test` to verify setup
5. Run `go run main.go` to start the development server

### Production Deployment
1. Build Docker image: `docker build -t deskcrm .`
2. Configure environment variables
3. Deploy with orchestration platform
4. Set up monitoring and alerting
5. Configure load balancing and scaling

This project demonstrates sophisticated Go microservice architecture with extensive external integrations, making it suitable for complex CRM and analytics workflows in educational technology environments.