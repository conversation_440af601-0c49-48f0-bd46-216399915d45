package course

import (
	"deskcrm/helpers"
	"fmt"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {

	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../../..")

	helpers.PreInit()

	helpers.PreInit()

	//validator
	helpers.InitValidator()

	// 初始化API客户端配置
	helpers.InitApiClient()

	helpers.InitMysql()
}

func TestCourseRecordConfigService_containsCourseId(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	service := CourseRecordConfigService{}
	result, err := service.GetShouldersAndTagsAndTabsByCourseId(ctx, 550387)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	fmt.Printf("result: %v\n", result)
}
