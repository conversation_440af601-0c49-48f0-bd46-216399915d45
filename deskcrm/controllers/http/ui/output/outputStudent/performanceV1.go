package outputStudent

// PerformanceV1Output 学生课程表现数据响应结构
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1 的输出结构
type PerformanceV1Output struct {
	SchemaId    string            `json:"schemaId"`    // 配置模式ID
	TableData   []any             `json:"tableData"`   // 章节数据数组
	TableHeader []TableHeaderItem `json:"tableHeader"` // 表头配置数组
}

// LessonTableRow 章节表格数据行结构
// 对应 PHP 版本的章节数据格式
type LessonTableRow struct {
	// 基础字段
	LessonId   string `json:"lessonId"`   // 章节ID
	LessonName string `json:"lessonName"` // 章节名称
	StartTime  string `json:"startTime"`  // 开始时间
	StopTime   string `json:"stopTime"`   // 结束时间
	PlayType   string `json:"playType"`   // 播放类型
	Type       string `json:"type"`       // 章节类型

	// 格式化时间字段
	InclassTime string `json:"inclassTime"` // 上课时间格式化 "Y-m-d H:i-H:i"

	// 学习数据字段
	Attend   string        `json:"attend"`   // 到课时长
	Preview  []interface{} `json:"preview"`  // [预习状态, 颜色标识, 是否可点击]
	Homework []interface{} `json:"homework"` // [作业状态, 颜色标识, 是否可点击]
	Exercise string        `json:"exercise"` // 互动题对答总
	Playback string        `json:"playback"` // 回放时长
	Score    string        `json:"score"`    // 学分

	// LPC 课程特有字段
	StrengthPracticeStatus string        `json:"strengthPracticeStatus"` // 巩固练习状态
	InClassHandsUpNum      int           `json:"inClassHandsUpNum"`      // 课中举手次数
	InClassVideoLinkNum    int           `json:"inClassVideoLinkNum"`    // 课中连麦次数
	LessonType             int           `json:"lessonType"`             // 课程类型
	TeacherName            string        `json:"teacherName"`            // 教师姓名
	LessonStartStatus      int           `json:"lessonStartStatus"`      // 课程开始状态
	HasQuestion            int           `json:"hasQuestion"`            // 是否有互动题
	HasPreview             int           `json:"hasPreview"`             // 是否有预习
	LessonReportUrl        []interface{} `json:"lessonReportUrl"`        // 课堂报告URL
	LessonReportStatus     int           `json:"lessonReportStatus"`     // 课堂报告状态

	// 传统课程字段（根据动态表头配置显示）
	PlaybackV1                         string `json:"playbackv1,omitempty"`                         // 回放情况V1
	LbpAttendDuration                  string `json:"lbpAttendDuration,omitempty"`                  // LBP到课时长
	LbpAttendDurationOld               string `json:"lbpAttendDurationOld,omitempty"`               // LBP到课时长旧版
	InclassTest                        string `json:"inclassTest,omitempty"`                        // 堂堂测
	OralQuestion                       string `json:"oralQuestion,omitempty"`                       // 口述题
	SimilarHomework                    string `json:"similarHomework,omitempty"`                    // 相似题作业
	ExerciseAll                        string `json:"exerciseAll,omitempty"`                        // 观看互动题
	LbpInteractExam                    string `json:"lbpInteractExam,omitempty"`                    // LBP互动题
	MixPlaybackInteract                string `json:"mixPlaybackInteract,omitempty"`                // 混合回放互动
	LittleKidFudaoHomeworkStatus       string `json:"littleKidFudaoHomeworkStatus,omitempty"`       // 小孩辅导作业状态
	LittleKidFudaoHomeworkLevel        string `json:"littleKidFudaoHomeworkLevel,omitempty"`        // 小孩辅导作业等级
	LittleKidFudaoInteract             string `json:"littleKidFudaoInteract,omitempty"`             // 小孩辅导互动
	SynchronousPractice                string `json:"synchronousPractice,omitempty"`                // 同步练习
	HasCompositionReport               string `json:"hasCompositionReport,omitempty"`               // 是否有作文报告
	Talk                               string `json:"talk,omitempty"`                               // 发言情况
	MonthlyExamReport                  string `json:"monthlyExamReport,omitempty"`                  // 月考报告URL
	IsInclassTeacherRoomAttend30minute string `json:"isInclassTeacherRoomAttend30minute,omitempty"` // 是否课中教师房间到课30分钟
	IsAttendFinish                     string `json:"isAttendFinish,omitempty"`                     // 是否到课完成
	GjkAttendLessonLubo                string `json:"gjkAttendLessonLubo,omitempty"`                // 高阶课到课录播
	GjkCompleteLessonLubo              string `json:"gjkCompleteLessonLubo,omitempty"`              // 高阶课完成录播
	GjkLessonTag                       string `json:"gjkLessonTag,omitempty"`                       // 高阶课章节标签

	// LPC专用动态表头字段
	LpcLessonName                string `json:"lpclessonName,omitempty"`                // LPC章节名称
	AttendStatus                 string `json:"attendStatus,omitempty"`                 // LPC到课状态
	FinishStatus                 string `json:"finishStatus,omitempty"`                 // LPC完成状态
	PlayStatus                   string `json:"playStatus,omitempty"`                   // LPC播放状态
	PreView                      string `json:"preView,omitempty"`                      // LPC预习
	TangtangExamStat             string `json:"tangtangExamStat,omitempty"`             // LPC堂堂测统计
	DeerEloquenceHomeworkLevel   string `json:"deerEloquenceHomeworkLevel,omitempty"`   // 小鹿口才作业等级
	DeerProgrammingHomeworkLevel string `json:"deerProgrammingHomeworkLevel,omitempty"` // 小鹿编程作业等级
	DeerLessonReportUrl          string `json:"deerLessonReportUrl,omitempty"`          // 小鹿课堂报告
	DeerLessonHomeWork           string `json:"deerLessonHomeWork,omitempty"`           // 小鹿课堂作业
	ZhiboLessonReportUrl         string `json:"zhiboLessonReportUrl,omitempty"`         // 直播课堂报告

	// 方舟规则动态字段（通过 map 扩展）
	ArkFields map[string]interface{} `json:"-"` // 方舟规则字段，在序列化时动态合并
}

// TableHeaderItem 表头配置项结构
// 对应 PHP 版本的表头配置格式
type TableHeaderItem struct {
	Label string `json:"label"` // 显示标签
	Prop  string `json:"prop"`  // 字段名
	Cname string `json:"cname"` // 组件名称
	Width int    `json:"width"` // 列宽
	Hover string `json:"hover"` // 悬停提示
	Sort  int    `json:"sort"`  // 排序
}

// ExamTestListOutput 试卷测试列表输出结构
// 对应 PHP Service_Page_DeskV1_Student_GetExamTestList 的输出
type ExamTestListOutput struct {
	TableData   []map[string]interface{} `json:"tableData"`   // 试卷数据
	TableHeader []TableHeaderItem        `json:"tableHeader"` // 试卷表头
}
