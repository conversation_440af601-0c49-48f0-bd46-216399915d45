package models

import (
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
)

var CourseRecordSchemaDao courseRecordSchemaDao

type courseRecordSchemaDao struct {
}

// CourseRecordSchema 课程记录配置表
type CourseRecordSchema struct {
	SchemaId        int64  `gorm:"column:schema_id;primaryKey;autoIncrement" json:"schemaId"`
	Name            string `gorm:"column:name" json:"name"`
	Schema          string `gorm:"column:schema" json:"schema"`                     // JSON格式的配置数据
	NewCourseTypes  string `gorm:"column:new_course_types" json:"newCourseTypes"`   // 新课程类型，逗号分隔
	CoursePriceTags string `gorm:"column:course_price_tags" json:"coursePriceTags"` // 课程价格标签，逗号分隔
	BusinessLineId  int64  `gorm:"column:business_line_id" json:"businessLineId"`   // 业务线ID
	Grades          string `gorm:"column:grades" json:"grades"`                     // 年级，逗号分隔
	Subjects        string `gorm:"column:subjects" json:"subjects"`                 // 学科，逗号分隔
	RuleName        string `gorm:"column:rule_name" json:"ruleName"`                // 规则名称
	CourseIds       string `gorm:"column:course_ids" json:"courseIds"`              // 课程ID，逗号分隔
	Type            int    `gorm:"column:type" json:"type"`                         // 类型：1-按规则生效，2-按课程生效，3-按合约生效
	Description     string `gorm:"column:description" json:"description"`           // 描述
	Status          int    `gorm:"column:status" json:"status"`                     // 状态：0-未上线，1-已上线，2-已下线
	OperatorUid     int64  `gorm:"column:operator_uid" json:"operatorUid"`          // 操作人UID
	Operator        string `gorm:"column:operator" json:"operator"`                 // 操作人姓名
	CreateTime      int64  `gorm:"column:create_time" json:"createTime"`            // 创建时间
	UpdateTime      int64  `gorm:"column:update_time" json:"updateTime"`            // 更新时间
}

func (CourseRecordSchema) TableName() string {
	return "tblCourseRecordSchema"
}

// GetListByTypeAndStatus 根据类型和状态获取记录列表
func (d *courseRecordSchemaDao) GetListByTypeAndStatus(ctx *gin.Context, recordType int, status int) ([]CourseRecordSchema, error) {
	var records []CourseRecordSchema

	db := helpers.MysqlClient.WithContext(ctx)
	query := db.Model(&CourseRecordSchema{}).
		Where("type = ?", recordType).
		Where("status = ?", status)

	err := query.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}

// GetListByBusinessLineIdsAndTypeStatus 根据业务线ID列表、类型和状态获取记录
func (d *courseRecordSchemaDao) GetListByBusinessLineIdsAndTypeStatus(ctx *gin.Context, businessLineIds []int64, recordType int, status int) ([]CourseRecordSchema, error) {
	var records []CourseRecordSchema

	db := helpers.MysqlClient.WithContext(ctx)
	query := db.Model(&CourseRecordSchema{}).
		Where("type = ?", recordType).
		Where("status = ?", status)

	if len(businessLineIds) > 0 {
		query = query.Where("business_line_id IN ?", businessLineIds)
	}

	err := query.Find(&records).Error
	if err != nil {
		return nil, err
	}

	return records, nil
}

// CourseInfo 课程信息结构，用于传递给业务方法
type CourseInfo struct {
	CourseId       int64 `json:"courseId"`
	Grade          int64 `json:"grade"`   // mainGradeId
	Subject        int64 `json:"subject"` // mainSubjectId
	NewCourseType  int   `json:"newCourseType"`
	Source         int64 `json:"source"`
	CoursePriceTag int   `json:"coursePriceTag"`
}
