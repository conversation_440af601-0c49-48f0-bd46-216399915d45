package moat

import (
	"deskcrm/api"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/libs/json"
	"deskcrm/libs/utils"
	"deskcrm/util"
	"fmt"
	"strings"
	"sync"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	OrderStatusFinish = 5  // 已完成的订单
	maxBatchSize      = 20 // 批量处理最大数量
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Moat,
	}
	return c
}

const (
	batchGetUserRolesAPI     = "/userrole/coreapi/batchgetuserroles"
	GetSkuListByConditionAPI = "/goodssearch/interface/getskulistbycondition"
	GetSkuBaseInfoAPI        = "/newgoodsplatform/sku/skubaseinfo"
	getTradeListApi          = "/one/open/search"
	getTradeDetailApi        = "/one/open/detail"
	oneOpenListApi           = "/one/open/list"
	GetThirdOrderSearch      = "/portals/api/third/order/search"
	afterDetailApi           = "/afterplat/after/detail"
	afterSearchApi           = "/afterplat/after/search"
	getGoodsSkuKVBySkuIdApi  = "/newgoodsplatform/sku/getgoodsskukvbyskuidv2"
	packSearchApi            = "/scpt/api/pack/search"
	darGetKVByCourseIdsApi   = "/one/adapter/dargetkvbycourseids"
	createShortUrlApi        = "/su/api/createshorturl"
)

func (c *Client) BatchGetUserRoles(ctx *gin.Context, studentUids []int64) map[int64][]int64 {
	chunks := components.ChunkArrayInt64(studentUids, 10)
	wg := &sync.WaitGroup{}
	ch := make(chan map[int64][]int64)
	for _, chunk := range chunks {
		wg.Add(1)
		chunkTemp := chunk
		go func(uids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "BatchGetUserRoles panic, err:%s", r)
					components.Util.PanicTrace(ctx)
				}
			}()
			defer wg.Done()
			singleRet, err := c.GetUserRoles(ctx, chunkTemp)
			if err != nil {
				zlog.Warnf(ctx, "BatchGetUserRoles failed, studentUid:%+v, err:%s", uids, err)
				return
			}
			zlog.Infof(ctx, "BatchGetUserRoles res, studentUid:%+v, err:%s", uids, err)

			ch <- singleRet
		}(chunk)
	}

	result := make(map[int64][]int64)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "BatchGetUserRoles panic, err:%s", r)
				components.Util.PanicTrace(ctx)
			}
		}()
		defer colWg.Done()
		for stuMap := range ch {
			for stuUid := range stuMap {
				result[stuUid] = stuMap[stuUid]
			}
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result
}

func (c *Client) GetUserRoles(ctx *gin.Context, studentUids []int64) (rileList map[int64][]int64, err error) {
	var resp BatchGetUserRolesResp
	params := map[string]interface{}{
		"userIds":   components.Array.JoinArrayInt64ToString(studentUids, ","),
		"appKey":    AppKey,
		"appSecret": AppSecret,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, batchGetUserRolesAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp.UserRoleList, nil
}

func (c *Client) GetSkuListByCourseIds(ctx *gin.Context, courseIds []int64) (result []GetSkuListByConditionItem, err error) {
	chunks := components.Array.ChunkArrayInt64(courseIds, components.SkuGetSize)
	wg := &sync.WaitGroup{}
	ch := make(chan []GetSkuListByConditionItem)
	for _, chunk := range chunks {
		wg.Add(1)
		go func(ids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetSkuListByCourseIds panic, err:%s", r)
					components.Util.PanicTrace(ctx)
				}
			}()
			defer wg.Done()
			conds := map[string]interface{}{
				"op": "and",
				"aggs": []map[string]interface{}{
					{
						"type": 0,
						"conds": map[string]interface{}{
							"key":   "thirdId",
							"value": ids,
							"exps":  "in",
						},
					},
					{
						"type": 0,
						"conds": map[string]interface{}{
							"key":   "skuMode",
							"value": components.SkuCourseMode,
							"exps":  "eq",
						},
					},
				},
			}

			pn := 0
			rn := components.SkuGetSize

			options := map[string]interface{}{
				"isCache": 0,
				"isAll":   1,
			}

			skuList, err := c.GetSkuListByCondition(ctx, conds, nil, pn*rn, rn, []string{"skuId", "spuId", "skuMode", "thirdId"}, options)
			if err != nil {
				return
			}

			zlog.Infof(ctx, "GetSkuListByCourseIds res, ids:%+v, err:%s", ids, err)

			ch <- skuList
		}(chunk)
	}

	result = make([]GetSkuListByConditionItem, 0)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "GetSkuListByCourseIds panic, err:%s", r)
				components.Util.PanicTrace(ctx)
			}
		}()
		defer colWg.Done()
		for skuInfo := range ch {
			result = append(result, skuInfo...)
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

func (c *Client) GetSkuListByCondition(ctx *gin.Context, conds map[string]interface{}, orders []interface{}, pn, rn int,
	sourceField []string, options map[string]interface{}) (result []GetSkuListByConditionItem, err error) {
	params := map[string]interface{}{
		"appKey":    AppKey,
		"appSecret": AppSecret,
		/*"source":        4,
		"saleChannelId": 99,*/
		"pn": pn,
		"rn": rn,
	}

	// 添加可选参数
	if len(conds) > 0 {
		jsonConds, _ := json.Marshal(conds)
		params["arrConds"] = string(jsonConds)
	}
	if len(orders) > 0 {
		jsonOrders, _ := json.Marshal(orders)
		params["arrOrder"] = string(jsonOrders)
	}
	if len(sourceField) > 0 {
		jsonSourceField, _ := json.Marshal(sourceField)
		params["sourceField"] = string(jsonSourceField)
	}
	if len(options) > 0 {
		for key, val := range options {
			params[key] = val
		}
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetSkuListByConditionAPI, opts)
	if err != nil {
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	var resp GetSkuListByConditionResp
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp.List, nil
}

func (c *Client) GetSkuBaseInfoBySkuIds(ctx *gin.Context, skuIds []int64) (result []SkuInfos, err error) {
	chunks := components.Array.ChunkArrayInt64(skuIds, components.SkuGetSize)
	wg := &sync.WaitGroup{}
	ch := make(chan []SkuInfos)
	for _, chunk := range chunks {
		wg.Add(1)
		chunkTemp := chunk
		go func(ids []int64) {
			defer func() {
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "GetSkuBaseInfoBySkuIds panic, err:%s", r)
					components.Util.PanicTrace(ctx)
				}
			}()
			defer wg.Done()

			skuInfoList, err := c.GetSkuBaseInfo(ctx, chunkTemp)
			if err != nil {
				return
			}

			zlog.Infof(ctx, "GetSkuBaseInfoBySkuIds res, skuIds:%+v, err:%s", ids, err)

			ch <- skuInfoList
		}(chunk)
	}

	result = make([]SkuInfos, 0)
	colWg := &sync.WaitGroup{}
	colWg.Add(1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				zlog.Errorf(ctx, "GetSkuBaseInfoBySkuIds panic, err:%s", r)
				components.Util.PanicTrace(ctx)
			}
		}()
		defer colWg.Done()
		for skuInfo := range ch {
			result = append(result, skuInfo...)
		}
	}()

	wg.Wait()
	close(ch)
	colWg.Wait()

	return result, nil
}

func (c *Client) GetSkuBaseInfo(ctx *gin.Context, skuIdList []int64) (listData []SkuInfos, err error) {
	if len(skuIdList) == 0 {
		return
	}

	var params = map[string]interface{}{
		"skuIdList": skuIdList,
		"appKey":    AppKey,
		"appSecret": AppSecret,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetSkuBaseInfoAPI, opts)
	if err != nil {
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	var resp SkuBaseInfoData
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	return resp.SkuInfoList, nil
}

func (c *Client) GetOneOpenSearch(ctx *gin.Context, studentIds []int64, skuIds []int64) (result []OneOpenSearchItem, err error) {
	// 分批处理用户ID
	chunks := components.ChunkArrayInt64(studentIds, 100)
	for _, chunk := range chunks {
		params := map[string]interface{}{
			"appKey":                  AppKey,
			"appSecret":               AppSecret,
			"userIdList":              chunk,
			"skuIds":                  skuIds,
			"orderBusinessStatusList": []int{OrderStatusFinish}, // 只获取已完成的订单
			"offset":                  0,
			"limit":                   len(chunk),
		}

		moatParams, signErr := SignParams(ctx, params)
		if signErr != nil {
			return nil, signErr
		}

		opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, getTradeListApi, opts)
		if err != nil {
			return nil, err
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		var resp GetOneOpenSearchResp
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return nil, err
		}

		result = append(result, resp.List...)
	}

	return result, nil
}

// GetOneOpenList 获取用户订单列表
func (c *Client) GetOneOpenList(ctx *gin.Context, userId int64, shopIds []int64, status int) ([]OneOpenListItem, error) {
	result := make([]OneOpenListItem, 0)
	page := 1
	size := 20
	for {
		params := map[string]interface{}{
			"appKey":    AppKey,
			"appSecret": AppSecret,
			"userId":    userId,
			"shopIds":   shopIds,
			"status":    status,
			"page":      page,
			"pageSize":  size,
		}

		moatParams, signErr := SignParams(ctx, params)
		if signErr != nil {
			return nil, signErr
		}

		opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpGet(ctx, oneOpenListApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "One GetOneOpenList request err: %v", err)
			return nil, err
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		var resp []OneOpenListItem
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return nil, err
		}

		result = append(result, resp...)

		// 如果返回数据少于 size，说明已经是最后一页
		if len(resp) < size {
			break
		}
		page++
	}

	zlog.Infof(ctx, "GetOneOpenList success: userId=%d, shopIds=%v, status=%d, result count=%d", userId, shopIds, status, len(result))
	return result, nil
}

// SearchAfterPlat 搜索售后订单
func (c *Client) SearchAfterPlat(ctx *gin.Context, userId int64, orderIds []int64) ([]AfterPlatSearchItem, error) {
	pageSize := 100
	var allInfos []AfterPlatSearchItem
	chunks := components.ChunkArrayInt64(orderIds, pageSize)
	for _, orderIdChunk := range chunks {
		page := 1
		for {
			orderIdListJson, _ := json.MarshalToString(orderIdChunk)

			params := map[string]interface{}{
				"appKey":      AppKey,
				"appSecret":   AppSecret,
				"userId":      userId,
				"orderIdList": orderIdListJson,
				"page":        page,
				"pageSize":    pageSize,
			}

			moatParams, signErr := SignParams(ctx, params)
			if signErr != nil {
				return nil, signErr
			}

			opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
			utils.DecorateHttpOptions(ctx, &opts)

			res, err := c.cli.HttpPost(ctx, afterSearchApi, opts)
			if err != nil {
				zlog.Warnf(ctx, "SearchAfterPlat request err: %v", err)
				return nil, err
			}

			if err = api.ApiHttpCode(ctx, res); err != nil {
				return nil, err
			}

			var resp AfterPlatSearchData
			if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
				return nil, err
			}

			allInfos = append(allInfos, resp.List...)

			// 如果返回数据少于 pageSize，说明已经是最后一页
			if len(resp.List) < pageSize {
				break
			}
			page++
		}
	}

	zlog.Infof(ctx, "SearchAfterPlat success: userId=%d, orderIds count=%d, result count=%d", userId, len(orderIds), len(allInfos))
	return allInfos, nil
}

func (c *Client) GetThirdOrderSearch(ctx *gin.Context, orderIds []int64) (result []ThirdOrderSearchItem, err error) {
	// 分批处理订单ID
	chunks := components.ChunkArrayInt64(orderIds, 20)
	for _, chunk := range chunks {
		params := map[string]interface{}{
			"appKey":    AppKey,
			"appSecret": AppSecret,
			"raw":       components.Array.JoinArrayInt64ToString(chunk, "|"),
		}

		moatParams, signErr := SignParams(ctx, params)
		if signErr != nil {
			return nil, signErr
		}

		opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, GetThirdOrderSearch, opts)
		if err != nil {
			return nil, err
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		var resp []ThirdOrderSearchItem
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return nil, err
		}

		result = append(result, resp...)
	}

	return
}

// AfterDetail 获取售后单详情，支持批量处理，每批最多20个售后单ID
func (c *Client) AfterDetail(ctx *gin.Context, userId int64, afterIds []string, option []string) (map[string]AfterDetail, error) {
	if userId == 0 || len(afterIds) == 0 {
		return make(map[string]AfterDetail), nil
	}

	zlog.Infof(ctx, "AfterPlat AfterDetail request: userId=%d, afterIds count=%d", userId, len(afterIds))

	result := make(map[string]AfterDetail)

	// 分批处理，每批最多20个
	for i := 0; i < len(afterIds); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(afterIds) {
			end = len(afterIds)
		}

		batch := afterIds[i:end]
		batchResult, err := c.afterDetailBatch(ctx, userId, batch, option)
		if err != nil {
			return nil, err
		}

		// 合并结果
		for k, v := range batchResult {
			result[k] = v
		}
	}

	zlog.Infof(ctx, "AfterPlat AfterDetail success: total after details=%d", len(result))
	return result, nil
}

// afterDetailBatch 批量获取售后详情（单批处理）
func (c *Client) afterDetailBatch(ctx *gin.Context, userId int64, afterIds []string, option []string) (map[string]AfterDetail, error) {
	result := make(map[string]AfterDetail)

	// 为每个售后单ID创建单独的请求参数
	for _, afterId := range afterIds {
		params := map[string]interface{}{
			"appKey":    AppKey,
			"appSecret": AppSecret,
			"userId":    userId,
			"afterId":   afterId,
			"option":    option,
		}

		moatParams, signErr := SignParams(ctx, params)
		if signErr != nil {
			return nil, signErr
		}

		opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, afterDetailApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "AfterPlat AfterDetail request err for afterId %s: %v", afterId, err)
			return nil, err
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		var resp AfterDetail
		if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
			return nil, err
		}

		result[afterId] = resp
	}

	return result, nil
}

// GetGoodsSkuKVBySkuId 根据SKU ID获取商品SKU信息，支持批量处理，每批最多20个SKU ID
func (c *Client) GetGoodsSkuKVBySkuId(ctx *gin.Context, skuIds []int64) (map[int64]SkuDetailInfo, error) {
	if len(skuIds) == 0 {
		return make(map[int64]SkuDetailInfo), nil
	}

	zlog.Infof(ctx, "GoodsPlatform GetGoodsSkuKVBySkuId request: skuIds count=%d", len(skuIds))

	result := make(map[int64]SkuDetailInfo)

	// 分批处理，每批最多20个
	for i := 0; i < len(skuIds); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(skuIds) {
			end = len(skuIds)
		}

		batch := skuIds[i:end]
		batchResult, err := c.getGoodsSkuKVBySkuIdBatch(ctx, batch)
		if err != nil {
			return nil, err
		}

		// 合并结果
		for k, v := range batchResult {
			result[k] = v
		}
	}

	zlog.Infof(ctx, "GoodsPlatform GetGoodsSkuKVBySkuId success: total skus=%d", len(result))
	return result, nil
}

// getGoodsSkuKVBySkuIdBatch 批量获取SKU信息（单批处理）
func (c *Client) getGoodsSkuKVBySkuIdBatch(ctx *gin.Context, skuIds []int64) (map[int64]SkuDetailInfo, error) {
	params := map[string]interface{}{
		"appKey":    AppKey,
		"appSecret": AppSecret,
		"skuIds":    skuIds,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getGoodsSkuKVBySkuIdApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "GoodsPlatform GetGoodsSkuKVBySkuId batch request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var skuList []SkuDetailInfo
	if _, err = api.DecodeResponse(ctx, res, &skuList); err != nil {
		return nil, err
	}

	// 将数组转换为map，以skuId作为key
	result := make(map[int64]SkuDetailInfo)
	for _, sku := range skuList {
		result[sku.SkuId] = sku
	}

	return result, nil
}

// Search 获取包裹物流信息
// 对应PHP的 Fudao_Scpt::Search 方法
func (c *Client) Search(ctx *gin.Context, orderIds []int64, expressNumbers []string) ([]ExpressInfo, error) {
	if len(orderIds) == 0 && len(expressNumbers) == 0 {
		zlog.Warnf(ctx, "Scpt Search called with empty parameters")
		return nil, fmt.Errorf("orderIds and expressNumbers cannot both be empty")
	}

	var result []ExpressInfo
	// 处理订单ID查询
	if len(orderIds) > 0 {
		// 分批处理，每批最多20个
		for i := 0; i < len(orderIds); i += maxBatchSize {
			end := i + maxBatchSize
			if end > len(orderIds) {
				end = len(orderIds)
			}

			batch := orderIds[i:end]
			batchResult, err := c.searchByOrderIds(ctx, batch)
			if err != nil {
				return nil, err
			}

			result = append(result, batchResult...)
		}
	}

	// 处理快递单号查询
	if len(expressNumbers) > 0 {
		// 分批处理，每批最多20个
		for i := 0; i < len(expressNumbers); i += maxBatchSize {
			end := i + maxBatchSize
			if end > len(expressNumbers) {
				end = len(expressNumbers)
			}

			batch := expressNumbers[i:end]
			batchResult, err := c.searchByExpressNumbers(ctx, batch)
			if err != nil {
				return nil, err
			}

			result = append(result, batchResult...)
		}
	}

	return result, nil
}

// searchByOrderIds 根据订单ID查询物流信息
func (c *Client) searchByOrderIds(ctx *gin.Context, orderIds []int64) ([]ExpressInfo, error) {

	params := map[string]interface{}{
		"appKey":    AppKey,
		"appSecret": AppSecret,
		"orderNo":   strings.Join(util.ConvertArrayIntToArrayString(orderIds), "|"),
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, packSearchApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "Scpt Search by orderIds request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp []ExpressInfo
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

// searchByExpressNumbers 根据快递单号查询物流信息
func (c *Client) searchByExpressNumbers(ctx *gin.Context, expressNumbers []string) ([]ExpressInfo, error) {
	params := map[string]interface{}{
		"appKey":        AppKey,
		"appSecret":     AppSecret,
		"expressNumber": strings.Join(expressNumbers, "|"),
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, packSearchApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "Scpt Search by expressNumbers request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp []ExpressInfo
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

// GetTradeList 获取订单列表
// 对应PHP的 Fudao_One::getTradeList 方法
func (c *Client) GetTradeList(ctx *gin.Context, userId int64, orderBusinessStatusList []int, orderTimeStart, orderTimeStop int64, offset, limit int, shopIdList []int64, arrOrder []map[string]string) (*TradeListResponse, error) {
	if userId <= 0 {
		return nil, fmt.Errorf("userId must be greater than 0")
	}

	// 构建请求参数
	params := map[string]interface{}{
		"appKey":     AppKey,
		"appSecret":  AppSecret,
		"userIdList": []int64{userId},
		"offset":     offset,
		"limit":      limit,
	}

	if len(orderBusinessStatusList) > 0 {
		params["orderBusinessStatusList"] = orderBusinessStatusList
	}
	if orderTimeStart > 0 {
		params["orderTimeStart"] = orderTimeStart
	}
	if orderTimeStop > 0 {
		params["orderTimeStop"] = orderTimeStop
	}
	if len(shopIdList) > 0 {
		params["shopIdList"] = shopIdList
	}
	if len(arrOrder) > 0 {
		params["arrOrder"] = arrOrder
	}

	zlog.Infof(ctx, "One GetTradeList request: userId=%d, offset=%d, limit=%d", userId, offset, limit)

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getTradeListApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "One GetTradeList request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp TradeListResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

// GetTradeDetail 获取订单详情
// 对应PHP的 Fudao_One::getTradeDetail 方法
// 支持批量处理，每批最多20个订单ID
func (c *Client) GetTradeDetail(ctx *gin.Context, userId int64, orderIds []int64, fields []string) ([]TradeDetailItem, error) {
	if userId <= 0 {
		return nil, fmt.Errorf("userId must be greater than 0")
	}
	if len(orderIds) == 0 {
		return nil, fmt.Errorf("orderIds cannot be empty")
	}

	zlog.Infof(ctx, "One GetTradeDetail request: userId=%d, orderIds count=%d", userId, len(orderIds))

	var result []TradeDetailItem
	// 分批处理，每批最多20个
	for i := 0; i < len(orderIds); i += maxBatchSize {
		end := i + maxBatchSize
		if end > len(orderIds) {
			end = len(orderIds)
		}

		batch := orderIds[i:end]
		batchResp, err := c.getTradeDetailBatch(ctx, userId, batch, fields)
		if err != nil {
			return nil, err
		}

		result = append(result, batchResp...)
	}

	return result, nil
}

// getTradeDetailBatch 批量获取订单详情（单批处理）
func (c *Client) getTradeDetailBatch(ctx *gin.Context, userId int64, orderIds []int64, fields []string) ([]TradeDetailItem, error) {
	params := map[string]interface{}{
		"appKey":    AppKey,
		"appSecret": AppSecret,
		"userId":    userId,
		"orderId":   orderIds,
		"fields":    fields,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getTradeDetailApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "One GetTradeDetail batch request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp []TradeDetailItem
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp, nil
}

// DarGetKVByCourseIds 根据课程id查询订单列表
// 支持批量处理，每批最多20个学员课程ID
func (c *Client) DarGetKVByCourseIds(ctx *gin.Context, studentCourseIds []string, fields []string) ([]DarKVByCourseIdsItem, error) {
	if len(studentCourseIds) == 0 {
		return nil, fmt.Errorf("studentCourseIds cannot be empty")
	}
	if len(fields) == 0 {
		return nil, fmt.Errorf("fields cannot be empty")
	}

	var result []DarKVByCourseIdsItem
	// 分批处理，每批最多20个
	for i := 0; i < len(studentCourseIds); i += maxBatchSize {
		end := min(i+maxBatchSize, len(studentCourseIds))

		batch := studentCourseIds[i:end]
		batchResp, err := c.darGetKVByCourseIdsBatch(ctx, batch, fields)
		if err != nil {
			return nil, err
		}

		result = append(result, batchResp...)
	}

	return result, nil
}

// darGetKVByCourseIdsBatch 批量获取课程订单KV信息（单批处理）
func (c *Client) darGetKVByCourseIdsBatch(ctx *gin.Context, studentCourseIds []string, fields []string) ([]DarKVByCourseIdsItem, error) {
	params := map[string]interface{}{
		"appKey":           AppKey,
		"appSecret":        AppSecret,
		"studentCourseIds": studentCourseIds,
		"fields":           fields,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, darGetKVByCourseIdsApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "One DarGetKVByCourseIds batch request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// api.DecodeResponse 会自动解析 errNo、errMsg 和 data 字段
	// 我们只需要传入 data 部分对应的结构体
	var dataResp map[string]DarKVByCourseIdsItem
	if _, err = api.DecodeResponse(ctx, res, &dataResp); err != nil {
		return nil, err
	}

	// 将map转换为slice
	var result []DarKVByCourseIdsItem
	for _, item := range dataResp {
		result = append(result, item)
	}

	return result, nil
}

// GetShortUrl 获取短链接
// 对应PHP版本的Api_Su::getShortUrl方法
func (c *Client) GetShortUrl(ctx *gin.Context, longUrl string) (string, error) {
	if longUrl == "" {
		return "", fmt.Errorf("longUrl不能为空")
	}

	params := map[string]interface{}{
		"longUrl":   longUrl,
		"appKey":    AppKey,
		"appSecret": AppSecret,
	}

	moatParams, signErr := SignParams(ctx, params)
	if signErr != nil {
		return "", signErr
	}

	opts := base.HttpRequestOptions{RequestBody: moatParams, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, createShortUrlApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetShortUrl request err: %v", err)
		return longUrl, nil // 失败时返回原链接，不返回错误
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		zlog.Warnf(ctx, "GetShortUrl http code err: %v", err)
		return longUrl, nil // 失败时返回原链接，不返回错误
	}

	var resp ShortUrlData
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		zlog.Warnf(ctx, "GetShortUrl decode response err: %v", err)
		return longUrl, nil // 失败时返回原链接，不返回错误
	}

	if resp.ShortUrl != "" {
		return resp.ShortUrl, nil
	}

	return longUrl, nil // 没有短链接时返回原链接
}
