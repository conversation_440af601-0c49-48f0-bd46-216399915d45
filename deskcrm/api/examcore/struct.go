package examcore

import (
	"fmt"
)

// 绑定类型常量 - 对应PHP中的BUY_TYPE_*
const (
	BindTypLesson   = 0 // 章节绑定
	BindTypeCourse  = 1 // 课程绑定
	BindTypeCpu     = 2 // CPU绑定
	BindTypeOutline = 3 // 大纲绑定
)

// 试卷类型常量 - 对应PHP中的EXAM_TYPE_*
const (
	ExamTypePracticeInClass  = 1  // 课中练习 别名：互动题
	ExamTypePreview          = 5  // 课前预习 小学预习 小学课前预习
	ExamTypePosttestMore     = 13 // 初高中预习测试
	ExamTypePracticeStrength = 7  // 巩固练习
	ExamTypePreTest          = 3  // 报前测试
	ExamTypePostTest         = 4  // 报后测试
	ExamTypeStage            = 9  // 阶段性测试
	ExamTypeSurvey           = 20 // 摸底测
	ExamTypeTestInClass      = 10 // 堂堂测
)

// 绑定类型和绑定名称关系映射 - 对应PHP中的$bindType2bindNameMap
var BindType2BindNameMap = map[int]string{
	BindTypLesson:   "lesson",
	BindTypeCourse:  "course",
	BindTypeCpu:     "cpu",
	BindTypeOutline: "outline",
}

// ExamcoreResponse examcore API通用响应格式
type ExamcoreResponse struct {
	List interface{} `json:"list"`
}

// BindInfo 试卷绑定信息
type BindInfo struct {
	ExamId   string                 `json:"examId"`   // 试卷ID
	ExamName string                 `json:"examName"` // 试卷名称
	UserKv   map[string]interface{} `json:"userKv"`   // 用户KV信息，包含passScore等
}

// AnswerInfo 学生作答信息
type AnswerInfo struct {
	AnswerId   int              `json:"answerId"`   // 作答ID
	ExamId     int              `json:"examId"`     // 试卷ID
	StudentUid int              `json:"studentUid"` // 学生UID
	Score      int              `json:"score"`      // 分数
	IsFinish   int              `json:"isFinish"`   // 是否完成
	CreateTime int              `json:"createTime"` // 创建时间
	UpdateTime int              `json:"updateTime"` // 更新时间
	Props      AnswerProps      `json:"props"`      // 扩展属性
	AnswerList []AnswerListItem `json:"answerList"` // 答题列表
}

// AnswerProps 作答扩展属性
type AnswerProps struct {
	Duration int `json:"duration"` // 耗时（秒）
}

// ExamInfo 试卷信息
type ExamInfo struct {
	ExamId   int    `json:"examId"`   // 试卷ID
	ExamName string `json:"examName"` // 试卷名称
	Status   int    `json:"status"`   // 状态
}

// ExamDetailInfo 试卷详细信息
type ExamDetailInfo struct {
	Exam ExamDetail `json:"exam"` // 试卷详情
}

// ExamDetail 试卷详情
type ExamDetail struct {
	ExamId     string             `json:"examId"`     // 试卷ID
	ExamName   string             `json:"examName"`   // 试卷名称
	Status     int                `json:"status"`     // 状态
	Subject    int                `json:"subject"`    // 学科ID
	Grade      int                `json:"grade"`      // 年级
	TotalScore int                `json:"totalScore"` // 总分
	ExamType   int                `json:"examType"`   // 试卷类型
	TidList    map[string]TidItem `json:"tidList"`    // 题目ID列表
	Props      ExamProps          `json:"props"`      // 扩展属性
}

// TidItem 题目项
type TidItem struct {
	Sort           int `json:"sort"`           // 排序
	Tid            int `json:"tid"`            // 题目ID
	TimuSourceType int `json:"timuSourceType"` // 题目来源类型
	Score          int `json:"score"`          // 分数
	Type           int `json:"type"`           // 题目类型
}

// ExamProps 试卷扩展属性
type ExamProps struct {
	Duration    int    `json:"duration"`    // 时长（秒）
	Requirement string `json:"requirement"` // 要求
	TotalScore  string `json:"totalScore"`  // 总分
}

// AnswerList 答题列表项
type AnswerListItem struct {
	Correct   int                    `json:"correct"`   // 是否正确：1-正确，0-错误
	Type      int                    `json:"type"`      // 题目类型
	JudgeInfo map[string]interface{} `json:"judgeInfo"` // 判题信息
}

// FormatBindKey 格式化绑定Key - 对应PHP中的formatBindKey方法
func FormatBindKey(bindId int64, bindType int) string {
	bindName := BindType2BindNameMap[bindType]
	return fmt.Sprintf("%s_%d", bindName, bindId)
}

// FormatBindStr 格式化绑定字符串 - 对应PHP中的formatBindStr方法
func FormatBindStr(bindId int64, bindType int, examType int) string {
	bindKey := FormatBindKey(bindId, bindType)
	return fmt.Sprintf("%s:%d", bindKey, examType)
}
